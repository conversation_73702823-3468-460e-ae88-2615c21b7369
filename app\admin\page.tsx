'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  MoreHorizontal,
  Check,
  X,
  Clock,
  AlertTriangle,
  Trash2,
  Shield,
  User,
  ExternalLink,
  Plus,
  Loader2
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define types
interface SystemSetting {
  id: string;
  value: any;
  createdAt: string;
  updatedAt: string;
}

interface ChainConfig {
  id: string;
  name: string;
}

interface TokenRequirements {
  selectedChain: string;
  chainConfigs: ChainConfig[];
  tokenAddresses: Record<string, string>;
  tokenNames: Record<string, string>;
  minimumHoldings: Record<string, string>;
  burnAmounts: Record<string, string>;
}

interface ProfileDefaults {
  default_role: string;
  default_status: string;
  default_expiry_days: string;
  default_profile_name_format: string;
  default_profile_bio: string;
}

interface ComponentDefault {
  componentType: string;
  order: string;
  hidden: string;
  details: {
    backgroundColor?: string;
    fontColor?: string | null;
    scale?: string;
    positionX?: number;
    positionY?: number;
    naturalWidth?: number | null;
    naturalHeight?: number | null;
    shape?: string;
    profileName?: string;
    profileBio?: string;
    defaultImagePath?: string;
    heroContent?: Array<{
      title: string;
      description: string;
      contentType: string;
      colorGradient?: string;
    }>;
    socialLinks?: {
      twitter?: string;
      discord?: string;
      telegram?: string;
      website?: string;
      facebook?: string;
      youtube?: string;
      email?: string;
      linkedin?: string;
      cro?: string;
    };
  };
}

interface ComponentDefaults {
  defaults: ComponentDefault[];
}

interface User {
  address: string;
  name?: string; // Optional for backward compatibility
  profileName?: string; // New field
  role: 'admin' | 'user';
  status: 'new' | 'in-progress' | 'pending' | 'approved' | 'deleted';
  expiryDate: string | null;
  transactionHash: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function AdminPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [featuredProfile, setFeaturedProfile] = useState<string>('web3tools');
  const [tokenRequirements, setTokenRequirements] = useState<TokenRequirements>({
    selectedChain: '25',
    chainConfigs: [],
    tokenAddresses: {},
    tokenNames: {},
    minimumHoldings: {},
    burnAmounts: {}
  });
  const [profileDefaults, setProfileDefaults] = useState<ProfileDefaults>({
    default_role: 'user',
    default_status: 'new',
    default_expiry_days: '1',
    default_profile_name_format: '',
    default_profile_bio: ''
  });
  const [componentDefaults, setComponentDefaults] = useState<ComponentDefaults>({ defaults: [] });
  const [loading, setLoading] = useState(true);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [activeTab, setActiveTab] = useState('users');
  const { address, isConnected } = useAccount();
  const router = useRouter();

  // Fetch system settings
  const fetchSettings = async () => {
    try {
      setSettingsLoading(true);
      const response = await fetch('/api/admin/settings');
      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }
      const data = await response.json();
      setSettings(data);

      // Parse token requirements
      const tokenReqSetting = data.find((setting: SystemSetting) => setting.id === 'token_requirements');
      if (tokenReqSetting) {
        setTokenRequirements(tokenReqSetting.value);
      }

      // Parse profile defaults
      const profileDefaultsSetting = data.find((setting: SystemSetting) => setting.id === 'profile_defaults');
      if (profileDefaultsSetting) {
        setProfileDefaults(profileDefaultsSetting.value);
      }

      // Parse component defaults
      const componentDefaultsSetting = data.find((setting: SystemSetting) => setting.id === 'component_defaults');
      if (componentDefaultsSetting) {
        setComponentDefaults(componentDefaultsSetting.value);
      }

      // Parse featured profile
      const featuredProfileSetting = data.find((setting: SystemSetting) => setting.id === 'featured_profile');
      if (featuredProfileSetting) {
        setFeaturedProfile(featuredProfileSetting.value);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('Failed to fetch system settings');
    } finally {
      setSettingsLoading(false);
    }
  };

  // Fetch users and check if current user is admin
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/users');
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }
        const data = await response.json();
        setUsers(data);

        // Check if current user is admin
        if (isConnected && address) {
          const currentUser = data.find((user: User) => user.address.toLowerCase() === address.toLowerCase());
          if (currentUser && currentUser.role === 'admin') {
            setIsAdmin(true);
          } else {
            setIsAdmin(false);
            // Redirect non-admin users
            router.push('/');
            toast.error('You do not have permission to access the admin page');
          }
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        toast.error('Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };

    if (isConnected) {
      fetchUsers();
      fetchSettings();
    } else {
      // Redirect if not connected
      router.push('/');
      toast.error('Please connect your wallet to access the admin page');
    }
  }, [isConnected, address, router]);

  // Update user role
  const updateUserRole = async (userAddress: string, role: 'admin' | 'user') => {
    try {
      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      });

      if (!response.ok) {
        throw new Error('Failed to update user role');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success(`User role updated to ${role}`);
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    }
  };

  // Update user status
  const updateUserStatus = async (userAddress: string, status: 'new' | 'in-progress' | 'pending' | 'approved' | 'deleted') => {
    try {
      // If status is approved, also set expiryDate to null
      const updateData: { status: string; expiryDate?: null } = { status };
      if (status === 'approved') {
        updateData.expiryDate = null;
      }

      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error('Failed to update user status');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success(`User status updated to ${status}`);
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    }
  };

  // Update transaction hash
  const updateTransactionHash = async (userAddress: string, transactionHash: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ transactionHash }),
      });

      if (!response.ok) {
        throw new Error('Failed to update transaction hash');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success('Transaction hash updated');
    } catch (error) {
      console.error('Error updating transaction hash:', error);
      toast.error('Failed to update transaction hash');
    }
  };

  // Update token requirements
  const updateTokenRequirements = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: 'token_requirements',
          value: tokenRequirements,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update token requirements');
      }

      toast.success('Token requirements updated successfully');
    } catch (error) {
      console.error('Error updating token requirements:', error);
      toast.error('Failed to update token requirements');
    }
  };

  // Update component defaults
  const updateComponentDefaults = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: 'component_defaults',
          value: componentDefaults,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update component defaults');
      }

      toast.success('Component defaults updated successfully');
    } catch (error) {
      console.error('Error updating component defaults:', error);
      toast.error('Failed to update component defaults');
    }
  };

  // Update profile defaults
  const updateProfileDefaults = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: 'profile_defaults',
          value: profileDefaults,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile defaults');
      }

      toast.success('Profile defaults updated successfully');
    } catch (error) {
      console.error('Error updating profile defaults:', error);
      toast.error('Failed to update profile defaults');
    }
  };

  // Update featured profile
  const updateFeaturedProfile = async () => {
    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: 'featured_profile',
          value: featuredProfile,
          address: address
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update featured profile');
      }

      toast.success('Featured profile updated successfully');
    } catch (error) {
      console.error('Error updating featured profile:', error);
      toast.error('Failed to update featured profile');
    }
  };

  // Update expiry date
  const updateExpiryDate = async (userAddress: string, expiryDate: string | null) => {
    try {
      const response = await fetch(`/api/admin/users/${userAddress}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ expiryDate }),
      });

      if (!response.ok) {
        throw new Error('Failed to update expiry date');
      }

      const updatedUser = await response.json();
      setUsers(users.map(user => user.address === userAddress ? updatedUser : user));
      toast.success('Expiry date updated');
    } catch (error) {
      console.error('Error updating expiry date:', error);
      toast.error('Failed to update expiry date');
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'in-progress':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'pending':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'approved':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'deleted':
        return <Trash2 className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-4 w-4 text-purple-500" />;
      case 'user':
        return <User className="h-4 w-4 text-blue-500" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Add new chain config
  const addChainConfig = () => {
    const newChainId = prompt('Enter new chain ID:');
    if (!newChainId) return;

    const newChainName = prompt('Enter chain name:');
    if (!newChainName) return;

    setTokenRequirements(prev => ({
      ...prev,
      chainConfigs: [...(prev.chainConfigs || []), { id: newChainId, name: newChainName }],
      tokenAddresses: { ...(prev.tokenAddresses || {}), [newChainId]: '' },
      tokenNames: { ...(prev.tokenNames || {}), [newChainId]: 'Web3Tools' },
      minimumHoldings: { ...(prev.minimumHoldings || {}), [newChainId]: '100' },
      burnAmounts: { ...(prev.burnAmounts || {}), [newChainId]: '10' }
    }));
  };

  // Set selected chain
  const setSelectedChain = (chainId: string) => {
    if (!chainId) return; // Don't update if chainId is empty

    setTokenRequirements(prev => ({
      ...prev,
      selectedChain: chainId
    }));
  };

  // Update token address for selected chain
  const updateTokenAddress = (value: string) => {
    setTokenRequirements(prev => {
      if (!prev.selectedChain) return prev;
      return {
        ...prev,
        tokenAddresses: {
          ...(prev.tokenAddresses || {}),
          [prev.selectedChain]: value
        }
      };
    });
  };

  // Update token name for selected chain
  const updateTokenName = (value: string) => {
    setTokenRequirements(prev => {
      if (!prev.selectedChain) return prev;
      return {
        ...prev,
        tokenNames: {
          ...(prev.tokenNames || {}),
          [prev.selectedChain]: value
        }
      };
    });
  };

  // Update minimum holdings for selected chain
  const updateMinimumHoldings = (value: string) => {
    setTokenRequirements(prev => {
      if (!prev.selectedChain) return prev;
      return {
        ...prev,
        minimumHoldings: {
          ...(prev.minimumHoldings || {}),
          [prev.selectedChain]: value
        }
      };
    });
  };

  // Update burn amount for selected chain
  const updateBurnAmount = (value: string) => {
    setTokenRequirements(prev => {
      if (!prev.selectedChain) return prev;
      return {
        ...prev,
        burnAmounts: {
          ...(prev.burnAmounts || {}),
          [prev.selectedChain]: value
        }
      };
    });
  };

  // Add component default
  const addComponentDefault = () => {
    setComponentDefaults(prev => ({
      defaults: [
        ...(prev.defaults || []),
        {
          componentType: '',
          order: '',
          hidden: 'N',
          details: {
            backgroundColor: 'transparent',
            fontColor: null
          }
        }
      ]
    }));
  };

  // Remove component default
  const removeComponentDefault = (index: number) => {
    setComponentDefaults(prev => ({
      defaults: (prev.defaults || []).filter((_, i) => i !== index)
    }));
  };

  // Update component default
  const updateComponentDefault = (index: number, field: keyof ComponentDefault, value: string) => {
    setComponentDefaults(prev => {
      if (!prev.defaults || prev.defaults.length <= index) {
        return prev; // Return unchanged if defaults is undefined or index is out of bounds
      }
      const newDefaults = [...prev.defaults];
      newDefaults[index] = {
        ...newDefaults[index],
        [field]: value
      };
      return { defaults: newDefaults };
    });
  };

  // Update component details
  const updateComponentDetails = (index: number, detailField: string, value: any) => {
    setComponentDefaults(prev => {
      if (!prev.defaults || prev.defaults.length <= index) {
        return prev; // Return unchanged if defaults is undefined or index is out of bounds
      }
      const newDefaults = [...prev.defaults];
      newDefaults[index] = {
        ...newDefaults[index],
        details: {
          ...(newDefaults[index].details || {}),
          [detailField]: value
        }
      };
      return { defaults: newDefaults };
    });
  };

  // Get Cronoscan URL
  const getCronoscanUrl = (hash: string) => {
    return `https://cronoscan.com/tx/${hash}`;
  };

  if (!isConnected) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
        <p>Please connect your wallet to access the admin page.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
        <p>Loading...</p>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>
        <p>You do not have permission to access this page.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Admin Dashboard</h1>

      <Tabs defaultValue="users" className="w-full mb-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="settings">System Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-4">
          <div className="rounded-md border">
        <Table>
          <TableCaption>List of all users in the system</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Address</TableHead>
              <TableHead>Profile Name</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Referral Code</TableHead>
              <TableHead>Referred By</TableHead>
              <TableHead>Expiry Date</TableHead>
              <TableHead>Transaction Hash</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users && users.length > 0 ? users.map((user) => (
              <TableRow key={user.address}>
                <TableCell className="font-medium">
                  {user.address.substring(0, 6)}...{user.address.substring(user.address.length - 4)}
                </TableCell>
                <TableCell>{user.profileName || user.name}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getRoleIcon(user.role)}
                    <span className="capitalize">{user.role}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(user.status)}
                    <span className="capitalize">{user.status}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {user.referralCode ? (
                    <span className="font-mono text-sm bg-neutral-100 dark:bg-neutral-800 px-2 py-1 rounded">
                      {user.referralCode}
                    </span>
                  ) : (
                    <span className="text-neutral-400">N/A</span>
                  )}
                </TableCell>
                <TableCell>
                  {user.referredBy ? (
                    <span className="font-mono text-sm text-blue-600 dark:text-blue-400">
                      {user.referredBy}
                    </span>
                  ) : (
                    <span className="text-neutral-400">N/A</span>
                  )}
                </TableCell>
                <TableCell>{formatDate(user.expiryDate)}</TableCell>
                <TableCell>
                  {user.transactionHash ? (
                    <a
                      href={getCronoscanUrl(user.transactionHash)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-blue-500 hover:underline"
                    >
                      {user.transactionHash.substring(0, 6)}...{user.transactionHash.substring(user.transactionHash.length - 4)}
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  ) : (
                    'N/A'
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />

                      {/* Role actions */}
                      <DropdownMenuLabel className="text-xs text-muted-foreground">Change Role</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => updateUserRole(user.address, 'admin')}>
                        <Shield className="mr-2 h-4 w-4" />
                        <span>Set as Admin</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserRole(user.address, 'user')}>
                        <User className="mr-2 h-4 w-4" />
                        <span>Set as User</span>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      {/* Status actions */}
                      <DropdownMenuLabel className="text-xs text-muted-foreground">Change Status</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'new')}>
                        <Clock className="mr-2 h-4 w-4 text-blue-500" />
                        <span>Set as New</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'in-progress')}>
                        <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                        <span>Set as In Progress</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'pending')}>
                        <AlertTriangle className="mr-2 h-4 w-4 text-orange-500" />
                        <span>Set as Pending</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'approved')}>
                        <Check className="mr-2 h-4 w-4 text-green-500" />
                        <span>Set as Approved</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => updateUserStatus(user.address, 'deleted')}>
                        <Trash2 className="mr-2 h-4 w-4 text-red-500" />
                        <span>Set as Deleted</span>
                      </DropdownMenuItem>

                      <DropdownMenuSeparator />

                      {/* Transaction hash action */}
                      <DropdownMenuItem
                        onClick={() => {
                          const hash = prompt('Enter transaction hash:');
                          if (hash) updateTransactionHash(user.address, hash);
                        }}
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        <span>Update Transaction Hash</span>
                      </DropdownMenuItem>

                      {/* Expiry date action */}
                      <DropdownMenuItem
                        onClick={() => {
                          const date = prompt('Enter expiry date (YYYY-MM-DD):');
                          if (date) {
                            const expiryDate = new Date(date);
                            if (!isNaN(expiryDate.getTime())) {
                              updateExpiryDate(user.address, expiryDate.toISOString());
                            } else {
                              toast.error('Invalid date format');
                            }
                          }
                        }}
                      >
                        <Clock className="mr-2 h-4 w-4" />
                        <span>Update Expiry Date</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )) : (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-4">No users found</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
        </TabsContent>

        <TabsContent value="settings" className="mt-4">
          <div className="grid gap-6 md:grid-cols-2 mb-6">
            {/* Token Requirements Card */}
            <Card>
              <CardHeader>
                <CardTitle>Token Requirements</CardTitle>
                <CardDescription>Configure minimum token holdings and burn amount</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="chain-select">Select Blockchain</Label>
                    <Select
                      value={tokenRequirements.selectedChain || '25'}
                      onValueChange={setSelectedChain}
                    >
                      <SelectTrigger id="chain-select" className="w-full">
                        <SelectValue placeholder="Select a blockchain" />
                      </SelectTrigger>
                      <SelectContent>
                        {tokenRequirements.chainConfigs?.map((chain) => (
                          <SelectItem key={chain.id} value={chain.id}>
                            {chain.name} (Chain ID: {chain.id})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground">Configure token requirements for each blockchain</p>
                  </div>

                  <div className="p-4 border rounded-md space-y-4">
                    <h4 className="font-medium">
                      {tokenRequirements.chainConfigs?.find(c => c.id === tokenRequirements.selectedChain)?.name || 'Chain'} Configuration
                    </h4>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="token-address">Token Address</Label>
                        <Input
                          id="token-address"
                          type="text"
                          value={tokenRequirements.tokenAddresses?.[tokenRequirements.selectedChain] || ''}
                          onChange={(e) => updateTokenAddress(e.target.value)}
                          placeholder="0x..."
                        />
                        <p className="text-sm text-muted-foreground">Contract address of the token on this blockchain</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="token-name">Token Name</Label>
                        <Input
                          id="token-name"
                          type="text"
                          value={tokenRequirements.tokenNames?.[tokenRequirements.selectedChain] || 'Web3Tools'}
                          onChange={(e) => updateTokenName(e.target.value)}
                          placeholder="e.g., Web3Tools"
                        />
                        <p className="text-sm text-muted-foreground">Name of the token to display to users</p>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="minimum-holdings">Minimum Holdings</Label>
                          <Input
                            id="minimum-holdings"
                            type="text"
                            value={tokenRequirements.minimumHoldings?.[tokenRequirements.selectedChain] || '100'}
                            onChange={(e) => updateMinimumHoldings(e.target.value)}
                            placeholder="e.g., 100"
                          />
                          <p className="text-sm text-muted-foreground">Minimum tokens required</p>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="burn-amount">Burn Amount</Label>
                          <Input
                            id="burn-amount"
                            type="text"
                            value={tokenRequirements.burnAmounts?.[tokenRequirements.selectedChain] || '10'}
                            onChange={(e) => updateBurnAmount(e.target.value)}
                            placeholder="e.g., 10"
                          />
                          <p className="text-sm text-muted-foreground">Tokens to burn for actions</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    onClick={addChainConfig}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Blockchain
                  </Button>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={updateTokenRequirements}>Save Token Settings</Button>
              </CardFooter>
            </Card>

            {/* Profile Defaults Card */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Defaults</CardTitle>
                <CardDescription>Configure default settings for new profiles</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="default_role">Default Role</Label>
                  <Input
                    id="default_role"
                    type="text"
                    value={profileDefaults.default_role}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_role: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default role for new users (user/admin)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_status">Default Status</Label>
                  <Input
                    id="default_status"
                    type="text"
                    value={profileDefaults.default_status}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_status: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default status for new users (new/in-progress/pending/approved/deleted)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_expiry_days">Default Expiry Days</Label>
                  <Input
                    id="default_expiry_days"
                    type="text"
                    value={profileDefaults.default_expiry_days}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_expiry_days: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default number of days until profile expiry</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_profile_name_format">Default Profile Name Format</Label>
                  <Input
                    id="default_profile_name_format"
                    type="text"
                    value={profileDefaults.default_profile_name_format}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_profile_name_format: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default format for profile names</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default_profile_bio">Default Profile Bio</Label>
                  <Input
                    id="default_profile_bio"
                    type="text"
                    value={profileDefaults.default_profile_bio}
                    onChange={(e) => setProfileDefaults({
                      ...profileDefaults,
                      default_profile_bio: e.target.value
                    })}
                  />
                  <p className="text-sm text-muted-foreground">Default bio text for new profiles</p>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={updateProfileDefaults}>Save Profile Defaults</Button>
              </CardFooter>
            </Card>
          </div>

          {/* Featured Profile Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Featured Profile</CardTitle>
              <CardDescription>Set the profile to display on the home page</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="featured-profile">Featured Profile Name</Label>
                  <Input
                    id="featured-profile"
                    value={featuredProfile}
                    onChange={(e) => setFeaturedProfile(e.target.value)}
                    placeholder="e.g., web3tools"
                  />
                  <p className="text-sm text-muted-foreground">
                    Enter the name of the profile you want to feature on the home page.
                    Make sure this profile exists in the database.
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={updateFeaturedProfile} disabled={settingsLoading}>
                {settingsLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Featured Profile'
                )}
              </Button>
            </CardFooter>
          </Card>

          {/* Component Defaults Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Component Defaults</CardTitle>
              <CardDescription>Configure default components for new profiles</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {componentDefaults.defaults?.map((component, index) => (
                  <div key={index} className="p-3 border rounded-md space-y-4">
                    <div className="flex items-end gap-2">
                      <div className="flex-1 space-y-2">
                        <Label htmlFor={`component-type-${index}`}>Component Type</Label>
                        <Input
                          id={`component-type-${index}`}
                          value={component.componentType}
                          onChange={(e) => updateComponentDefault(index, 'componentType', e.target.value)}
                          placeholder="e.g., banner, profilePicture"
                        />
                      </div>
                      <div className="w-20 space-y-2">
                        <Label htmlFor={`order-${index}`}>Order</Label>
                        <Input
                          id={`order-${index}`}
                          value={component.order}
                          onChange={(e) => updateComponentDefault(index, 'order', e.target.value)}
                          placeholder="e.g., 1, 2"
                        />
                      </div>
                      <div className="w-24 space-y-2">
                        <Label htmlFor={`hidden-${index}`}>Hidden</Label>
                        <Input
                          id={`hidden-${index}`}
                          value={component.hidden}
                          onChange={(e) => updateComponentDefault(index, 'hidden', e.target.value)}
                          placeholder="Y/N"
                        />
                      </div>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => removeComponentDefault(index)}
                        className="mb-0.5"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Component Details Section */}
                    <div className="space-y-3 border-t pt-3">
                      <h4 className="text-sm font-medium">Component Details</h4>

                      {/* Common details for all components */}
                      <div className="grid grid-cols-2 gap-2">
                        <div className="space-y-1">
                          <Label htmlFor={`bg-color-${index}`} className="text-xs">Background Color</Label>
                          <Input
                            id={`bg-color-${index}`}
                            value={component.details?.backgroundColor || 'transparent'}
                            onChange={(e) => updateComponentDetails(index, 'backgroundColor', e.target.value)}
                            placeholder="transparent"
                            className="h-8 text-sm"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor={`font-color-${index}`} className="text-xs">Font Color</Label>
                          <Input
                            id={`font-color-${index}`}
                            value={component.details?.fontColor || ''}
                            onChange={(e) => updateComponentDetails(index, 'fontColor', e.target.value || null)}
                            placeholder="null"
                            className="h-8 text-sm"
                          />
                        </div>
                      </div>

                      {/* Note: Image positioning is now handled in the componentImages table */}

                      {/* Profile Picture specific details */}
                      {component.componentType === 'profilePicture' && (
                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label htmlFor={`shape-${index}`} className="text-xs">Shape</Label>
                            <Input
                              id={`shape-${index}`}
                              value={component.details?.shape || 'circular'}
                              onChange={(e) => updateComponentDetails(index, 'shape', e.target.value)}
                              placeholder="circular"
                              className="h-8 text-sm"
                            />
                          </div>
                          <div className="space-y-1">
                            <Label htmlFor={`default-image-${index}`} className="text-xs">Default Image Path</Label>
                            <Input
                              id={`default-image-${index}`}
                              value={component.details?.defaultImagePath || 'pfp.jpg'}
                              onChange={(e) => updateComponentDetails(index, 'defaultImagePath', e.target.value)}
                              placeholder="pfp.jpg"
                              className="h-8 text-sm"
                            />
                          </div>
                        </div>
                      )}

                      {/* Banner specific details */}
                      {component.componentType === 'banner' && (
                        <div className="space-y-1">
                          <Label htmlFor={`default-banner-${index}`} className="text-xs">Default Image Path</Label>
                          <Input
                            id={`default-banner-${index}`}
                            value={component.details?.defaultImagePath || 'banner.png'}
                            onChange={(e) => updateComponentDetails(index, 'defaultImagePath', e.target.value)}
                            placeholder="banner.png"
                            className="h-8 text-sm"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <Button
                  variant="outline"
                  onClick={addComponentDefault}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Component
                </Button>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={updateComponentDefaults}>Save Component Defaults</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
