'use client';

import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { SolanaAdapter } from "@reown/appkit-adapter-solana/react";
import { cronos, solana, solanaTestnet, solanaDevnet } from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";

export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Define all networks for multichain support
export const networks = [
  cronos,
  // Temporarily commenting out Solana networks to debug RPC issues
  // solana,
  // solanaTestnet,
  // solanaDevnet,
] as any;

// Define custom RPC URLs for Solana networks
const customRpcUrls = {
  // Solana Mainnet - using alternative RPC
  'solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp': [
    { url: 'https://solana-api.projectserum.com' }
  ],
  // Solana Testnet
  'solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z': [
    { url: 'https://api.testnet.solana.com' }
  ],
  // Solana Devnet - using alternative RPC
  'solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1': [
    { url: 'https://api.devnet.solana.com' }
  ]
};

// Create the Wagmi adapter (pass all networks, it will filter EVM ones)
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks, // Pass all networks, Wagmi will handle EVM ones
  customRpcUrls,
});

// Create the Solana adapter with proper RPC configuration
export const solanaAdapter = new SolanaAdapter();

// Create wagmi config (for compatibility)
export const wagmiConfig = wagmiAdapter.wagmiConfig;

// Create AppKit instance (temporarily EVM only for debugging)
createAppKit({
  adapters: [wagmiAdapter], // Only Wagmi adapter for now
  networks,
  projectId,
  // customRpcUrls, // Temporarily disabled
  features: {
    swaps: false,
    onramp: false,
    email: false, // Temporarily disable to avoid 403 errors
    socials: false,
    history: false,
    analytics: false,
    allWallets: false,
  },
  // Add metadata to help with CORS and image loading
  metadata: {
    name: 'Web3 Socials',
    description: 'Web3 Social Profile Platform',
    url: typeof window !== 'undefined' ? window.location.origin : 'https://localhost:3000',
    icons: ['https://avatars.githubusercontent.com/u/179229932']
  },
  // Disable features that might cause 403 errors
  enableWalletConnect: true,
  enableNetworkSwitch: true,
  // Add debug mode to see more details about errors
  debug: true,
});

export const config = wagmiConfig;
