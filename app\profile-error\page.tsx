'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAccount } from 'wagmi';
import Link from 'next/link';
import Image from 'next/image';
import { AlertTriangle, Clock, X, ExternalLink, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SimpleWalletValidator from '@/components/SimpleWalletValidator';
import { fetchTokenRequirements } from '@/lib/tokenRequirements';

export default function ProfileErrorPage() {
  const searchParams = useSearchParams();
  const status = searchParams?.get('status') || 'unknown';
  const profileName = searchParams?.get('name') || '';
  const { isConnected, address, chain } = useAccount();
  const [transactionHash, setTransactionHash] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [isCheckingOwnership, setIsCheckingOwnership] = useState(false);
  const [tokenRequirements, setTokenRequirements] = useState<{
    tokenAddress: string;
    tokenName: string;
    minimumHoldings: string;
    burnAmount: string;
  } | null>(null);
  const [isLoadingRequirements, setIsLoadingRequirements] = useState(true);

  // Fetch token requirements
  useEffect(() => {
    async function getTokenRequirements() {
      try {
        setIsLoadingRequirements(true);
        // Default to Cronos chain (25)
        const chainId = chain?.id?.toString() || '25';
        const requirements = await fetchTokenRequirements(chainId);
        setTokenRequirements(requirements);
      } catch (error) {
        // Error fetching token requirements
      } finally {
        setIsLoadingRequirements(false);
      }
    }

    getTokenRequirements();
  }, [chain]);

  // Check if the connected wallet is the profile owner
  useEffect(() => {
    async function checkOwnership() {
      if (!isConnected || !address || !profileName) {
        setIsOwner(false);
        return;
      }

      try {
        setIsCheckingOwnership(true);
        console.log(`Checking ownership for profile: ${profileName} with wallet: ${address}`);
        const response = await fetch(`/api/profile/check-ownership?name=${profileName}&walletAddress=${address}`);
        const data = await response.json();

        console.log('Ownership check result:', data);
        setIsOwner(data.isOwner);
      } catch (error) {
        console.error('Error checking ownership:', error);
        setIsOwner(false);
      } finally {
        setIsCheckingOwnership(false);
      }
    }

    checkOwnership();
  }, [address, profileName, isConnected]);

  // Function to get status icon
  const getStatusIcon = () => {
    switch (status) {
      case 'new':
        return <Clock className="h-12 w-12 text-blue-500" />;
      case 'in-progress':
        return <Clock className="h-12 w-12 text-yellow-500" />;
      case 'pending':
        return <AlertTriangle className="h-12 w-12 text-orange-500" />;
      case 'deleted':
        return <X className="h-12 w-12 text-red-500" />;
      case 'expired':
        return <Clock className="h-12 w-12 text-blue-500" />;
      default:
        return <AlertTriangle className="h-12 w-12 text-red-500" />;
    }
  };

  // Function to get status title
  const getStatusTitle = () => {
    switch (status) {
      case 'new':
        return 'Action Needed';
      case 'in-progress':
        return 'Profile In Progress';
      case 'pending':
        return 'Profile Pending Approval';
      case 'deleted':
        return 'Profile Deleted';
      case 'expired':
        return 'Profile Expired - Action Needed';
      default:
        return 'Profile Not Available';
    }
  };

  // Function to get status message
  const getStatusMessage = () => {
    switch (status) {
      case 'new':
        return (
          <>
            Hi, welcome to Web3Socials! This is a community project and in order for you to view your own webpage (https://Web3Socials.fun/{profileName || 'name'}), we require you to <span className="font-bold">BURN <span className="underline">{tokenRequirements?.burnAmount || 'X'} {tokenRequirements?.tokenName || 'Web3Tools'}</span></span> to help our community. For more inquiries, contact us on X.
          </>
        );
      case 'in-progress':
        return (
          <>
            Hi there! Please be patient as your transaction is being processed. If you have any questions, please DM us on X at https://x.com/Web3Tools_fun.
          </>
        );
      case 'pending':
        return (
          <>
            Hi there! Please be patient as your transaction is being reviewed. If you have any questions, please DM us on X at https://x.com/Web3Tools_fun.
          </>
        );
      case 'deleted':
        return (
          <>
            This profile has been deleted. Please contact an admin for assistance.
          </>
        );
      case 'expired':
        return (
          <>
            Hi, welcome back to Web3Socials! Your profile has expired. In order for you to view your webpage (https://Web3Socials.fun/{profileName || 'name'}), we require you to <span className="font-bold">BURN <span className="underline">{tokenRequirements?.burnAmount || 'X'} {tokenRequirements?.tokenName || 'Web3Tools'}</span></span> to help our community. For more inquiries, contact us on X.
          </>
        );
      default:
        return (
          <>
            This profile is not available. Please check the address or name and try again.
          </>
        );
    }
  };

  // Function to submit transaction hash
  const submitTransactionHash = async () => {
    // Reset error message
    setErrorMessage(null);

    // Check if transaction hash is provided
    if (!transactionHash) {
      setErrorMessage('Please enter a transaction hash.');
      return;
    }

    // Check if the connected wallet is the profile owner
    if (!isOwner) {
      setErrorMessage('You can only submit a transaction hash for your own profile.');
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch('/api/profile/submit-transaction', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address, // Use the connected wallet address
          transactionHash,
          referralCode: referralCode || undefined, // Include referral code if provided
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit transaction hash');
      }

      setSubmitSuccess(true);
      setTransactionHash('');
      setReferralCode('');
    } catch (error) {
      // Error submitting transaction hash
      setErrorMessage(error instanceof Error ? error.message : 'Failed to submit transaction hash. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <Link href="/discover" className="inline-flex items-center text-blue-500 hover:text-blue-700 mb-8">
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Discover
      </Link>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="flex flex-col items-center text-center mb-8">
          {status === 'new' || status === 'in-progress' || status === 'pending' ? (
            <div className="relative w-24 h-24 rounded-full overflow-hidden mb-4">
              <Image
                src="/pfp.jpg"
                alt="Web3Tools Profile"
                width={96}
                height={96}
                className="object-cover"
              />
            </div>
          ) : (
            getStatusIcon()
          )}
          <h1 className="text-2xl font-bold mt-4">{getStatusTitle()}</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 max-w-2xl">
            {isLoadingRequirements && status === 'new' ? 'Loading token requirements...' : getStatusMessage()}
          </p>
          {(status === 'new' || status === 'in-progress' || status === 'pending') && (
            <a
              href="https://x.com/Web3Tools_fun"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Contact us on X
            </a>
          )}
        </div>

        {(status === 'new' || status === 'expired') && (
          <SimpleWalletValidator
            fallbackContent={
              <div className="mt-8 max-w-md mx-auto">
                <h2 className="text-lg font-semibold mb-4">Submit Transaction Hash</h2>
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
                  Please connect your wallet to submit a transaction hash.
                </div>
              </div>
            }
          >
            <div className="mt-8 max-w-md mx-auto">
              <h2 className="text-lg font-semibold mb-4">Submit Transaction Hash</h2>

              {!profileName ? (
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
                  Profile name information is missing. Please try accessing this page from the discover page.
                </div>
              ) : isCheckingOwnership ? (
                <div className="bg-blue-100 dark:bg-blue-900/30 border border-blue-300 dark:border-blue-800 text-blue-800 dark:text-blue-200 rounded-md p-4 mb-4">
                  Checking profile ownership...
                </div>
              ) : !isOwner ? (
                <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
                  <p className="mb-2">You can only submit a transaction hash for your own profile. Please connect with the wallet that owns this profile.</p>
                  <p className="text-xs">Profile: {profileName}</p>
                  <p className="text-xs">Connected wallet: {address ? `${address.substring(0, 6)}...${address.substring(address.length - 4)}` : 'Not connected'}</p>
                </div>
              ) : submitSuccess ? (

              <div className="bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-800 text-green-800 dark:text-green-200 rounded-md p-4 mb-4">
                Transaction hash submitted successfully! Your profile will be reviewed soon.
              </div>
            ) : (
              <>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                  Please <strong className="font-bold">BURN {tokenRequirements?.burnAmount || 'X'} {tokenRequirements?.tokenName || 'Web3Tools'}</strong> tokens and provide the transaction hash below:
                </p>

                {errorMessage && (
                  <div className="bg-red-100 dark:bg-red-900/30 border border-red-300 dark:border-red-800 text-red-800 dark:text-red-200 rounded-md p-4 mb-4">
                    {errorMessage}
                  </div>
                )}

                <div className="space-y-4">
                  <div>
                    <label htmlFor="transaction-hash" className="block text-sm font-medium mb-1">
                      Transaction Hash
                    </label>
                    <input
                      id="transaction-hash"
                      type="text"
                      value={transactionHash}
                      onChange={(e) => setTransactionHash(e.target.value)}
                      placeholder="0x..."
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                    />
                  </div>

                  <div>
                    <label htmlFor="referral-code" className="block text-sm font-medium mb-1">
                      Referral Code (Optional)
                    </label>
                    <input
                      id="referral-code"
                      type="text"
                      value={referralCode}
                      onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                      placeholder="w3tXXXXX"
                      maxLength={8}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter a referral code if someone referred you to Web3Socials
                    </p>
                  </div>

                  <Button
                    onClick={submitTransactionHash}
                    disabled={!transactionHash || isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Transaction Hash'}
                  </Button>
                </div>
              </>
            )}
            </div>
          </SimpleWalletValidator>
        )}
      </div>
    </div>
  );
}
